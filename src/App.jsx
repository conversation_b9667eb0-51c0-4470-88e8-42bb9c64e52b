import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom'
import LandingPage from './pages/LandingPage'
import Dashboard from './pages/Dashboard'
import WelcomeModal from './components/WelcomeModal'
import CardPackModal from './components/CardPackModal'
import AudioControls from './components/AudioControls'
import { LanguageProvider } from './contexts/LanguageContext'
import { getChildName, hasOpenedStarterPack } from './utils/localStorage'
import { playBackgroundMusic, resumeAudioContext } from './utils/audio'

function App() {
  const [currentPage, setCurrentPage] = useState('landing')
  const [childName, setChildName] = useState('')
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [showCardPackModal, setShowCardPackModal] = useState(false)

  useEffect(() => {
    const savedChildName = getChildName()
    if (savedChildName) {
      setChildName(savedChildName)
      // Check if user has opened starter pack
      if (hasOpenedStarterPack(savedChildName)) {
        setCurrentPage('dashboard')
      } else {
        setShowCardPackModal(true)
        setCurrentPage('dashboard')
      }
    }

    // Initialize audio on app load
    const initializeAudio = async () => {
      // Resume audio context for browsers that suspend it
      resumeAudioContext()

      // Try to start background music
      await playBackgroundMusic()
    }

    initializeAudio()

    // Add click listener to resume audio on first user interaction
    const handleFirstInteraction = () => {
      resumeAudioContext()
      playBackgroundMusic()
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
    }

    document.addEventListener('click', handleFirstInteraction)
    document.addEventListener('touchstart', handleFirstInteraction)

    return () => {
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
    }
  }, [])

  const handleStartAdventure = () => {
    const savedChildName = getChildName()
    if (savedChildName) {
      setChildName(savedChildName)
      if (hasOpenedStarterPack(savedChildName)) {
        setCurrentPage('dashboard')
      } else {
        setShowCardPackModal(true)
        setCurrentPage('dashboard')
      }
    } else {
      setShowWelcomeModal(true)
    }
  }

  const handleWelcomeComplete = (name) => {
    setChildName(name)
    setShowWelcomeModal(false)
    setShowCardPackModal(true)
    setCurrentPage('dashboard')
  }

  const handleCardPackComplete = () => {
    setShowCardPackModal(false)
  }

  return (
    <Router>
      <LanguageProvider>
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500">
          {currentPage === 'landing' && (
            <LandingPage onStartAdventure={handleStartAdventure} />
          )}

          {currentPage === 'dashboard' && (
            <Dashboard childName={childName} />
          )}

          {showWelcomeModal && (
            <WelcomeModal
              onComplete={handleWelcomeComplete}
              onClose={() => setShowWelcomeModal(false)}
            />
          )}

          {showCardPackModal && (
            <CardPackModal
              childName={childName}
              onComplete={handleCardPackComplete}
              onClose={() => setShowCardPackModal(false)}
            />
          )}

          {/* Audio Controls */}
          <AudioControls />
        </div>
      </LanguageProvider>
    </Router>
  )
}

export default App
