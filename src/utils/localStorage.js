// Child name management
export const getChildName = () => {
  return localStorage.getItem('gameApp_childName') || ''
}

export const setChildName = (name) => {
  localStorage.setItem('gameApp_childName', name)
}

// Starter pack management
export const hasOpenedStarterPack = (childName) => {
  return localStorage.getItem(`gameApp_hasOpenedStarterPack_${childName}`) === 'true'
}

export const setStarterPackOpened = (childName) => {
  localStorage.setItem(`gameApp_hasOpenedStarterPack_${childName}`, 'true')
}

// Image collection management with themes
export const getImageCollection = (childName) => {
  const key = `gameApp_imageCollection_${childName}`
  const stored = localStorage.getItem(key)

  if (stored) {
    const collection = JSON.parse(stored)
    // Migrate old structure to new theme-based structure
    if (collection.starter && !collection.themes) {
      return {
        themes: [
          {
            id: 'starter',
            name: 'Starter Pack',
            description: 'Your first collection of amazing pictures!',
            images: collection.starter || [],
            createdAt: new Date().toISOString(),
            isDefault: true,
            color: 'from-blue-500 to-purple-600'
          },
          {
            id: 'generated',
            name: 'My Creations',
            description: 'Pictures I created with AI',
            images: collection.generated || [],
            createdAt: new Date().toISOString(),
            isDefault: true,
            color: 'from-green-500 to-teal-600'
          }
        ]
      }
    }
    return collection
  }

  // Return default structure with themes
  return {
    themes: [
      {
        id: 'starter',
        name: 'Starter Pack',
        description: 'Your first collection of amazing pictures!',
        images: [],
        createdAt: new Date().toISOString(),
        isDefault: true,
        color: 'from-blue-500 to-purple-600'
      }
    ]
  }
}

export const setImageCollection = (childName, collection) => {
  const key = `gameApp_imageCollection_${childName}`
  localStorage.setItem(key, JSON.stringify(collection))
}

// Theme management functions
export const createNewTheme = (childName, themeName, description = '', prompt = '') => {
  const collection = getImageCollection(childName)
  const newTheme = {
    id: `theme_${Date.now()}`,
    name: themeName,
    description: description,
    prompt: prompt,
    images: [],
    createdAt: new Date().toISOString(),
    isDefault: false,
    color: getRandomThemeColor()
  }

  collection.themes.push(newTheme)
  setImageCollection(childName, collection)
  return newTheme.id
}

export const updateThemeName = (childName, themeId, newName) => {
  const collection = getImageCollection(childName)
  const theme = collection.themes.find(t => t.id === themeId)
  if (theme && !theme.isDefault) {
    theme.name = newName
    setImageCollection(childName, collection)
  }
}

export const updateThemeDescription = (childName, themeId, newDescription) => {
  const collection = getImageCollection(childName)
  const theme = collection.themes.find(t => t.id === themeId)
  if (theme) {
    theme.description = newDescription
    setImageCollection(childName, collection)
  }
}

export const deleteTheme = (childName, themeId) => {
  const collection = getImageCollection(childName)
  const themeIndex = collection.themes.findIndex(t => t.id === themeId && !t.isDefault)
  if (themeIndex !== -1) {
    collection.themes.splice(themeIndex, 1)
    setImageCollection(childName, collection)
  }
}

export const addImageToTheme = (childName, themeId, imageData) => {
  const collection = getImageCollection(childName)
  const theme = collection.themes.find(t => t.id === themeId)

  if (theme) {
    const newImage = {
      id: `img_${Date.now()}`,
      name: imageData.name,
      dataUrl: imageData.dataUrl,
      url: imageData.url,
      prompt: imageData.prompt,
      createdAt: new Date().toISOString()
    }
    theme.images.push(newImage)
    setImageCollection(childName, collection)
  }
}

export const addGeneratedImage = (childName, imageData, themeId = null) => {
  const collection = getImageCollection(childName)

  // If themeId is provided, add to that theme
  if (themeId) {
    addImageToTheme(childName, themeId, imageData)
    return
  }

  // Otherwise, add to "My Creations" theme or create it if it doesn't exist
  let myCreationsTheme = collection.themes.find(t => t.id === 'generated')

  if (!myCreationsTheme) {
    myCreationsTheme = {
      id: 'generated',
      name: 'My Creations',
      description: 'Pictures I created with AI',
      images: [],
      createdAt: new Date().toISOString(),
      isDefault: true,
      color: 'from-green-500 to-teal-600'
    }
    collection.themes.push(myCreationsTheme)
  }

  addImageToTheme(childName, 'generated', imageData)
}

export const getRandomThemeColor = () => {
  const colors = [
    'from-pink-500 to-rose-600',
    'from-purple-500 to-indigo-600',
    'from-blue-500 to-cyan-600',
    'from-green-500 to-emerald-600',
    'from-yellow-500 to-orange-600',
    'from-red-500 to-pink-600',
    'from-indigo-500 to-purple-600',
    'from-teal-500 to-blue-600'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// Game progress management
export const getGameProgress = (childName) => {
  const key = `gameApp_gameProgress_${childName}`
  const stored = localStorage.getItem(key)

  if (stored) {
    return JSON.parse(stored)
  }

  // Return default structure
  return {
    memoryCard: { level: 1, bestScore: null, gamesPlayed: 0 },
    puzzle: { completed: 0, favoriteImage: null, bestTime: null },
    slidingPuzzle: { bestTime: null, difficulty: 'easy', solved: 0 }
  }
}

export const setGameProgress = (childName, progress) => {
  const key = `gameApp_gameProgress_${childName}`
  localStorage.setItem(key, JSON.stringify(progress))
}

export const updateGameProgress = (childName, gameType, updates) => {
  const progress = getGameProgress(childName)
  progress[gameType] = { ...progress[gameType], ...updates }
  setGameProgress(childName, progress)
}

// Achievements management
export const getAchievements = (childName) => {
  const key = `gameApp_achievements_${childName}`
  const stored = localStorage.getItem(key)

  if (stored) {
    return JSON.parse(stored)
  }

  // Return default structure
  return {
    starterPack: { unlocked: false, date: null },
    artist: { unlocked: false, progress: 0, target: 5 },
    genius: { unlocked: false, progress: 0, target: 10 }
  }
}

export const setAchievements = (childName, achievements) => {
  const key = `gameApp_achievements_${childName}`
  localStorage.setItem(key, JSON.stringify(achievements))
}

export const unlockAchievement = (childName, achievementKey, additionalData = {}) => {
  const achievements = getAchievements(childName)
  achievements[achievementKey] = {
    ...achievements[achievementKey],
    unlocked: true,
    date: new Date().toISOString(),
    ...additionalData
  }
  setAchievements(childName, achievements)
}

// Default starter images
export const getStarterImages = () => {
  return [
    { id: 'starter_1', name: 'Picture 1', url: '/images/img1.jpg' },
    { id: 'starter_2', name: 'Picture 2', url: '/images/img2.jpg' },
    { id: 'starter_3', name: 'Picture 3', url: '/images/img3.jpg' },
    { id: 'starter_4', name: 'Picture 4', url: '/images/img4.jpg' },
    { id: 'starter_5', name: 'Picture 5', url: '/images/img5.jpg' },
    { id: 'starter_6', name: 'Picture 6', url: '/images/img6.jpg' }
  ]
}

export const initializeStarterPack = (childName) => {
  const collection = getImageCollection(childName)

  // Find or create starter theme
  let starterTheme = collection.themes.find(t => t.id === 'starter')
  if (!starterTheme) {
    starterTheme = {
      id: 'starter',
      name: 'Starter Pack',
      description: 'Your first collection of amazing pictures!',
      images: [],
      createdAt: new Date().toISOString(),
      isDefault: true,
      color: 'from-blue-500 to-purple-600'
    }
    collection.themes.push(starterTheme)
  }

  // Add starter images to the theme
  const starterImages = getStarterImages()
  starterTheme.images = starterImages.map(img => ({
    ...img,
    createdAt: new Date().toISOString()
  }))

  setImageCollection(childName, collection)
  setStarterPackOpened(childName)
  unlockAchievement(childName, 'starterPack')
}
