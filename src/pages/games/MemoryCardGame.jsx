import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>Left, RotateCcw, Trophy, Star, Clock, Target } from 'lucide-react'
import {
  getImageCollection,
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playSuccessSound, playCardMatchSound, playSparkleSound, playCardFlipSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'

// Sparkle component for celebration effect
const Sparkle = ({ delay = 0 }) => {
  return (
    <div
      className="absolute pointer-events-none"
      style={{
        animation: `sparkle 2s ease-in-out ${delay}s infinite`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
      }}
    >
      <div className="text-yellow-400 text-2xl animate-pulse">✨</div>
    </div>
  )
}

const MemoryCardGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, paused, completed
  const [cards, setCards] = useState([])
  const [flippedCards, setFlippedCards] = useState([])
  const [matchedCards, setMatchedCards] = useState([])
  const [sparklingCards, setSparklingCards] = useState([]) // For sparkling animation
  const [moves, setMoves] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy') // easy: 4x3, medium: 4x4, hard: 6x4
  const [availableImages, setAvailableImages] = useState([])
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings
  const difficultySettings = {
    easy: { pairs: 6, gridCols: 4, gridRows: 3, timeBonus: 100 },
    medium: { pairs: 8, gridCols: 4, gridRows: 4, timeBonus: 150 },
    hard: { pairs: 12, gridCols: 6, gridRows: 4, timeBonus: 200 }
  }

  useEffect(() => {
    if (childName) {
      // Load available images
      const collection = getImageCollection(childName)
      const allImages = collection.themes?.reduce((acc, theme) => {
        return [...acc, ...theme.images]
      }, []) || []
      setAvailableImages(allImages)

      // Load game progress
      const progress = getGameProgress(childName)
      setGameProgress(progress.memoryCard || { level: 1, bestScore: null, gamesPlayed: 0 })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Check for matches
  useEffect(() => {
    if (flippedCards.length === 2) {
      const [first, second] = flippedCards
      if (cards[first].imageId === cards[second].imageId) {
        // Match found
        playCardMatchSound()

        // Add sparkling effect
        setSparklingCards([first, second])

        // Play sparkle sound with slight delay
        setTimeout(() => {
          playSparkleSound()
        }, 200)

        // Remove sparkling effect after animation
        setTimeout(() => {
          setSparklingCards([])
        }, 2000)

        setMatchedCards(prev => [...prev, first, second])
        setFlippedCards([])
        setScore(prev => prev + 100 + (timeElapsed < 60 ? 50 : 0)) // Time bonus
      } else {
        // No match
        setTimeout(() => {
          setFlippedCards([])
        }, 1000)
      }
      setMoves(prev => prev + 1)
    }
  }, [flippedCards, cards, timeElapsed])

  // Check for game completion
  useEffect(() => {
    if (matchedCards.length === cards.length && cards.length > 0) {
      setGameState('completed')
      const finalScore = score + difficultySettings[difficulty].timeBonus - timeElapsed
      setScore(finalScore)

      // Update progress
      const newProgress = {
        ...gameProgress,
        gamesPlayed: (gameProgress.gamesPlayed || 0) + 1,
        bestScore: Math.max(gameProgress.bestScore || 0, finalScore),
        level: Math.min((gameProgress.level || 1) + (finalScore > (gameProgress.bestScore || 0) ? 1 : 0), 10)
      }

      updateGameProgress(childName, 'memoryCard', newProgress)
      setGameProgress(newProgress)

      // Check for achievements
      if (newProgress.gamesPlayed >= 5) {
        unlockAchievement(childName, 'memoryMaster')
      }
    }
  }, [matchedCards, cards, score, difficulty, gameProgress, childName, timeElapsed])

  const shuffleArray = (array) => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  const startGame = useCallback(() => {
    if (availableImages.length < difficultySettings[difficulty].pairs) {
      alert(t('needMoreImages'))
      return
    }

    playClickSound()

    // Select random images for the game
    const selectedImages = shuffleArray(availableImages)
      .slice(0, difficultySettings[difficulty].pairs)

    // Create card pairs
    const cardPairs = selectedImages.flatMap((image, index) => [
      { id: index * 2, imageId: image.id, image: image },
      { id: index * 2 + 1, imageId: image.id, image: image }
    ])

    // Shuffle cards
    const shuffledCards = shuffleArray(cardPairs)

    setCards(shuffledCards)
    setFlippedCards([])
    setMatchedCards([])
    setSparklingCards([])
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(Date.now())
    setGameState('playing')
  }, [availableImages, difficulty, t])

  const flipCard = (index) => {
    if (
      gameState !== 'playing' ||
      flippedCards.length >= 2 ||
      flippedCards.includes(index) ||
      matchedCards.includes(index)
    ) {
      return
    }

    playCardFlipSound()
    setFlippedCards(prev => [...prev, index])
  }

  const resetGame = () => {
    playClickSound()
    setGameState('menu')
    setCards([])
    setFlippedCards([])
    setMatchedCards([])
    setSparklingCards([])
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(null)
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-bold">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">🃏 {t('memoryCardGame')}</h1>
              <p className="text-white/80">{t('memoryCardDesc')}</p>
            </div>

            <div className="w-32"></div>
          </div>

          {/* Game Stats */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8">
            <div className="grid grid-cols-3 gap-6 text-center text-white">
              <div>
                <div className="text-2xl font-bold">{gameProgress.level || 1}</div>
                <div className="text-sm opacity-80">{t('level')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{gameProgress.bestScore || 0}</div>
                <div className="text-sm opacity-80">{t('bestScore')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{gameProgress.gamesPlayed || 0}</div>
                <div className="text-sm opacity-80">{t('gamesPlayed')}</div>
              </div>
            </div>
          </div>

          {/* Difficulty Selection */}
          <div className="bg-white rounded-3xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    difficulty === level
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-2">{t(level)}</div>
                    <div className="text-sm text-gray-600">
                      {settings.pairs} {t('pairs')} • {settings.gridCols}×{settings.gridRows}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="text-center">
              <button
                onClick={startGame}
                disabled={availableImages.length < difficultySettings[difficulty].pairs}
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {t('startGame')}
              </button>

              {availableImages.length < difficultySettings[difficulty].pairs && (
                <p className="text-red-500 text-sm mt-2">
                  {t('needMoreImages')} ({availableImages.length}/{difficultySettings[difficulty].pairs})
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen p-4 relative"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/30 via-purple-500/30 to-pink-500/30"></div>

      {/* CSS for sparkle animation */}
      <style jsx>{`
        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }

        @keyframes celebration {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .celebration-card {
          animation: celebration 0.6s ease-in-out;
        }

        .sparkle-container {
          position: absolute;
          inset: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 1rem;
        }
      `}</style>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Game Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
            <span className="font-bold">{t('backToMenu')}</span>
          </button>

          {/* Game Stats */}
          <div className="flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3">
            <div className="flex items-center gap-2 text-white">
              <Clock className="w-5 h-5" />
              <span className="font-bold">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Target className="w-5 h-5" />
              <span className="font-bold">{moves} {t('moves')}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Star className="w-5 h-5" />
              <span className="font-bold">{score}</span>
            </div>
          </div>

          <button
            onClick={resetGame}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <RotateCcw className="w-6 h-6" />
            <span className="font-bold">{t('restart')}</span>
          </button>
        </div>

        {/* Game Board */}
        <div
          className="grid gap-6 mx-auto"
          style={{
            gridTemplateColumns: `repeat(${difficultySettings[difficulty].gridCols}, 1fr)`,
            maxWidth: `${difficultySettings[difficulty].gridCols * 160}px`
          }}
        >
          {cards.map((card, index) => {
            const isFlipped = flippedCards.includes(index) || matchedCards.includes(index)
            const isSparklingCard = sparklingCards.includes(index)

            return (
              <div
                key={card.id}
                onClick={() => flipCard(index)}
                className={`aspect-square cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                  isSparklingCard ? 'celebration-card' : ''
                }`}
                style={{ perspective: '1000px' }}
              >
                {/* Card Container with 3D flip effect */}
                <div
                  className={`
                    relative w-full h-full transition-transform duration-700
                    ${isFlipped ? 'rotate-y-180' : ''}
                    ${matchedCards.includes(index) ? 'ring-4 ring-green-400 rounded-2xl' : ''}
                  `}
                  style={{
                    transformStyle: 'preserve-3d',
                    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
                  }}
                >
                  {/* Sparkle Effect for Matched Cards */}
                  {isSparklingCard && (
                    <div className="sparkle-container">
                      {[...Array(8)].map((_, i) => (
                        <Sparkle key={i} delay={i * 0.2} />
                      ))}
                    </div>
                  )}

                  {/* Card Back */}
                  <div
                    className="absolute inset-0 w-full h-full rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg"
                    style={{ backfaceVisibility: 'hidden' }}
                  >
                    <div className="text-6xl drop-shadow-lg">🎴</div>
                  </div>

                  {/* Card Front */}
                  <div
                    className="absolute inset-0 w-full h-full rounded-2xl bg-white p-2 shadow-lg"
                    style={{
                      backfaceVisibility: 'hidden',
                      transform: 'rotateY(180deg)'
                    }}
                  >
                    <img
                      src={card.image.url || card.image.dataUrl}
                      alt={card.image.name}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('congratulations')}</h2>
              <p className="text-gray-600 mb-6">{t('memoryGameCompleted')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('time')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{moves}</div>
                    <div className="text-sm text-gray-600">{t('moves')}</div>
                  </div>
                  <div className="col-span-2">
                    <div className="text-3xl font-bold text-purple-600">{score}</div>
                    <div className="text-sm text-gray-600">{t('finalScore')}</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={startGame}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MemoryCardGame
