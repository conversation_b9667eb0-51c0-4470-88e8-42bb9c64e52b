import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>Left, RotateCcw, Trophy, Star, Clock, Shuffle } from 'lucide-react'
import {
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playSuccessSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'

const SlidingPuzzleGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, completed
  const [board, setBoard] = useState([])
  const [emptyPosition, setEmptyPosition] = useState({ row: 0, col: 0 })
  const [moves, setMoves] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy') // easy: 3x3, medium: 4x4, hard: 5x5
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings
  const difficultySettings = {
    easy: { size: 3, timeBonus: 300 },
    medium: { size: 4, timeBonus: 500 },
    hard: { size: 5, timeBonus: 800 }
  }

  useEffect(() => {
    if (childName) {
      // Load game progress
      const progress = getGameProgress(childName)
      setGameProgress(progress.slidingPuzzle || { bestTime: null, difficulty: 'easy', solved: 0 })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Check for completion
  useEffect(() => {
    if (gameState === 'playing' && board.length > 0) {
      const size = difficultySettings[difficulty].size
      const isCompleted = board.every((row, rowIndex) =>
        row.every((cell, colIndex) => {
          if (rowIndex === size - 1 && colIndex === size - 1) {
            return cell === 0 // Empty cell should be at bottom right
          }
          return cell === rowIndex * size + colIndex + 1
        })
      )

      if (isCompleted) {
        setGameState('completed')
        playSuccessSound()

        const finalScore = Math.max(difficultySettings[difficulty].timeBonus - timeElapsed - moves, 50)
        setScore(finalScore)

        // Update progress
        const newProgress = {
          ...gameProgress,
          solved: (gameProgress.solved || 0) + 1,
          bestTime: gameProgress.bestTime ? Math.min(gameProgress.bestTime, timeElapsed) : timeElapsed,
          difficulty: difficulty
        }

        updateGameProgress(childName, 'slidingPuzzle', newProgress)
        setGameProgress(newProgress)

        // Check for achievements
        if (newProgress.solved >= 5) {
          unlockAchievement(childName, 'slidingMaster')
        }
      }
    }
  }, [board, gameState, difficulty, timeElapsed, moves, gameProgress, childName])

  const createBoard = useCallback((size) => {
    // Create solved board
    const solvedBoard = []
    for (let i = 0; i < size; i++) {
      const row = []
      for (let j = 0; j < size; j++) {
        if (i === size - 1 && j === size - 1) {
          row.push(0) // Empty cell
        } else {
          row.push(i * size + j + 1)
        }
      }
      solvedBoard.push(row)
    }
    return solvedBoard
  }, [])

  const shuffleBoard = useCallback((board) => {
    const size = board.length
    let newBoard = board.map(row => [...row])
    let emptyRow = size - 1
    let emptyCol = size - 1

    // Perform random valid moves to shuffle
    for (let i = 0; i < 1000; i++) {
      const possibleMoves = []

      // Check all four directions
      if (emptyRow > 0) possibleMoves.push({ row: emptyRow - 1, col: emptyCol })
      if (emptyRow < size - 1) possibleMoves.push({ row: emptyRow + 1, col: emptyCol })
      if (emptyCol > 0) possibleMoves.push({ row: emptyRow, col: emptyCol - 1 })
      if (emptyCol < size - 1) possibleMoves.push({ row: emptyRow, col: emptyCol + 1 })

      if (possibleMoves.length > 0) {
        const randomMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)]

        // Swap empty cell with the selected cell
        newBoard[emptyRow][emptyCol] = newBoard[randomMove.row][randomMove.col]
        newBoard[randomMove.row][randomMove.col] = 0

        emptyRow = randomMove.row
        emptyCol = randomMove.col
      }
    }

    return { board: newBoard, emptyPosition: { row: emptyRow, col: emptyCol } }
  }, [])

  const startGame = useCallback(() => {
    playClickSound()
    const size = difficultySettings[difficulty].size
    const solvedBoard = createBoard(size)
    const { board: shuffledBoard, emptyPosition } = shuffleBoard(solvedBoard)

    setBoard(shuffledBoard)
    setEmptyPosition(emptyPosition)
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(Date.now())
    setGameState('playing')
  }, [difficulty, createBoard, shuffleBoard])

  const canMove = (row, col) => {
    const rowDiff = Math.abs(row - emptyPosition.row)
    const colDiff = Math.abs(col - emptyPosition.col)
    return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)
  }

  const moveCell = (row, col) => {
    if (!canMove(row, col) || gameState !== 'playing') return

    playClickSound()

    const newBoard = board.map(r => [...r])

    // Swap the clicked cell with the empty cell
    newBoard[emptyPosition.row][emptyPosition.col] = newBoard[row][col]
    newBoard[row][col] = 0

    setBoard(newBoard)
    setEmptyPosition({ row, col })
    setMoves(prev => prev + 1)
  }

  const resetGame = () => {
    playClickSound()
    setGameState('menu')
    setBoard([])
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(null)
  }

  const shuffleCurrentBoard = () => {
    if (gameState !== 'playing') return

    playClickSound()
    const { board: shuffledBoard, emptyPosition } = shuffleBoard(board)
    setBoard(shuffledBoard)
    setEmptyPosition(emptyPosition)
    setMoves(prev => prev + 10) // Penalty for shuffling
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div
        className="min-h-screen p-4 relative"
        style={{
          backgroundImage: 'url(/images/background-game.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Background Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"></div>

        <div className="max-w-4xl mx-auto relative z-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-bold">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">🔄 {t('slidingPuzzle')}</h1>
              <p className="text-white/80">{t('slidingDesc')}</p>
            </div>

            <div className="w-32"></div>
          </div>

          {/* Game Stats */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8">
            <div className="grid grid-cols-3 gap-6 text-center text-white">
              <div>
                <div className="text-2xl font-bold">{gameProgress.solved || 0}</div>
                <div className="text-sm opacity-80">{t('puzzlesSolved')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {gameProgress.bestTime ? formatTime(gameProgress.bestTime) : '--:--'}
                </div>
                <div className="text-sm opacity-80">{t('bestTime')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold capitalize">{gameProgress.difficulty || 'easy'}</div>
                <div className="text-sm opacity-80">{t('lastDifficulty')}</div>
              </div>
            </div>
          </div>

          {/* Difficulty Selection */}
          <div className="bg-white rounded-3xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    difficulty === level
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-2">{t(level)}</div>
                    <div className="text-sm text-gray-600">
                      {settings.size}×{settings.size} {t('grid')}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="text-center">
              <button
                onClick={startGame}
                className="bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200"
              >
                {t('startGame')}
              </button>
            </div>

            {/* How to Play */}
            <div className="mt-8 bg-gray-50 rounded-2xl p-6">
              <h3 className="font-bold text-gray-800 mb-3">{t('howToPlay')}</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• {t('slidingRule1')}</li>
                <li>• {t('slidingRule2')}</li>
                <li>• {t('slidingRule3')}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const size = difficultySettings[difficulty].size

  return (
    <div
      className="min-h-screen p-4 relative"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"></div>

      <div className="max-w-4xl mx-auto relative z-10">
        {/* Game Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
            <span className="font-bold">{t('backToMenu')}</span>
          </button>

          {/* Game Stats */}
          <div className="flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3">
            <div className="flex items-center gap-2 text-white">
              <Clock className="w-5 h-5" />
              <span className="font-bold">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Trophy className="w-5 h-5" />
              <span className="font-bold">{moves} {t('moves')}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={shuffleCurrentBoard}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <Shuffle className="w-6 h-6" />
              <span className="font-bold">{t('shuffle')}</span>
            </button>
            <button
              onClick={resetGame}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <RotateCcw className="w-6 h-6" />
              <span className="font-bold">{t('restart')}</span>
            </button>
          </div>
        </div>

        {/* Game Board */}
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
            <div
              className="grid gap-2 mx-auto"
              style={{
                gridTemplateColumns: `repeat(${size}, 1fr)`,
                width: `${Math.min(400, window.innerWidth - 100)}px`,
                aspectRatio: '1'
              }}
            >
              {board.map((row, rowIndex) =>
                row.map((cell, colIndex) => (
                  <button
                    key={`${rowIndex}-${colIndex}`}
                    onClick={() => moveCell(rowIndex, colIndex)}
                    disabled={cell === 0}
                    className={`
                      aspect-square rounded-xl font-bold text-xl transition-all duration-200 transform
                      ${cell === 0
                        ? 'bg-transparent cursor-default'
                        : canMove(rowIndex, colIndex)
                          ? 'bg-white hover:bg-gray-100 hover:scale-105 shadow-lg cursor-pointer text-gray-800'
                          : 'bg-white/70 text-gray-600 cursor-default shadow-md'
                      }
                      ${cell !== 0 && canMove(rowIndex, colIndex) ? 'hover:shadow-xl' : ''}
                    `}
                  >
                    {cell !== 0 && cell}
                  </button>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('puzzleSolved')}</h2>
              <p className="text-gray-600 mb-6">{t('excellentWork')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('time')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{moves}</div>
                    <div className="text-sm text-gray-600">{t('moves')}</div>
                  </div>
                  <div className="col-span-2">
                    <div className="text-3xl font-bold text-orange-600">{score}</div>
                    <div className="text-sm text-gray-600">{t('finalScore')}</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={startGame}
                  className="flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SlidingPuzzleGame
