import { createContext, useContext, useState, useEffect } from 'react'

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

const translations = {
  id: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Selamat datang kembali, {name}! 🌟',
    searchGames: 'Cari permainan...',

    // Picture Collection
    myPictureCollection: '📸 KOLEKSI GAMBARKU ({count})',
    myThemeCollections: '🎨 KOLEKSI TEMA GAMBARKU',
    organizeYourPictures: 'Atur gambar-gambarmu berdasarkan tema',
    generateNewImage: 'BUAT GAMBAR BARU',
    viewByThemes: 'Lihat berdasarkan tema',
    seeAll: 'Lihat semua →',

    // Theme Management
    createNewTheme: 'Buat Tema Baru',
    themeName: '<PERSON><PERSON> Tema',
    themeDescription: '<PERSON><PERSON><PERSON><PERSON> Tema',
    enterThemeName: 'Masukkan nama tema...',
    enterThemeDescription: 'Ceritakan tentang tema ini...',
    optional: 'opsional',
    cancel: 'Batal',
    create: 'Buat',
    pictures: 'gambar',
    generate: 'Buat',
    noImagesYet: 'Belum ada gambar',
    selectTheme: 'Pilih Tema',

    // Image Generation
    createAmazingPictures: 'Buat gambar menakjub dengan AI',
    describeYourPicture: 'Ceritakan gambar yang kamu inginkan',
    promptPlaceholder: 'Contoh: Seekor kucing lucu bermain di taman yang penuh bunga warna-warni...',
    generating: 'Sedang membuat',
    generateImage: 'Buat Gambar',
    generateAnother: 'Buat Lagi',
    saveToCollection: 'Simpan ke Koleksi',

    // Games Section
    startPlaying: '🎮 MULAI BERMAIN',
    memoryCardGame: 'Permainan Kartu Memori',
    memoryCardDesc: 'Cocokkan pasangan kartu',
    puzzleGame: 'Permainan Puzzle',
    puzzleDesc: 'Seret & lepas potongan puzzle',
    slidingPuzzle: 'Puzzle Geser',
    slidingDesc: 'Geser angka untuk menyelesaikan',
    start: 'MULAI',
    continue: 'LANJUTKAN',
    new: 'BARU!',
    level: 'LV',

    // Achievements
    yourAchievements: '🏆 PENCAPAIANMU',
    starterTitle: '🎁 Pemula',
    starterDesc: 'Membuka paket kartu pertama!',
    artistTitle: '🎨 Seniman',
    artistDesc: 'Buat 5 gambar',
    geniusTitle: '🧠 Jenius',
    geniusDesc: 'Selesaikan 10 permainan',
    unlocked: '✅ TERBUKA',
    locked: '⏳ TERKUNCI',
    progress: 'Progres',

    // Language Toggle
    language: 'Bahasa',
    indonesian: 'Indonesia',
    english: 'English'
  },
  en: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Welcome back, {name}! 🌟',
    searchGames: 'Search games...',

    // Picture Collection
    myPictureCollection: '📸 MY PICTURE COLLECTION ({count})',
    myThemeCollections: '🎨 MY THEME COLLECTIONS',
    organizeYourPictures: 'Organize your pictures by themes',
    generateNewImage: 'GENERATE NEW IMAGE',
    viewByThemes: 'View by themes',
    seeAll: 'See all →',

    // Theme Management
    createNewTheme: 'Create New Theme',
    themeName: 'Theme Name',
    themeDescription: 'Theme Description',
    enterThemeName: 'Enter theme name...',
    enterThemeDescription: 'Tell us about this theme...',
    optional: 'optional',
    cancel: 'Cancel',
    create: 'Create',
    pictures: 'pictures',
    generate: 'Generate',
    noImagesYet: 'No images yet',
    selectTheme: 'Select Theme',

    // Image Generation
    createAmazingPictures: 'Create amazing pictures with AI',
    describeYourPicture: 'Describe the picture you want',
    promptPlaceholder: 'Example: A cute cat playing in a garden full of colorful flowers...',
    generating: 'Generating',
    generateImage: 'Generate Image',
    generateAnother: 'Generate Another',
    saveToCollection: 'Save to Collection',

    // Games Section
    startPlaying: '🎮 START PLAYING',
    memoryCardGame: 'Memory Card Game',
    memoryCardDesc: 'Match pairs of cards',
    puzzleGame: 'Puzzle Game',
    puzzleDesc: 'Drag & drop puzzle pieces',
    slidingPuzzle: 'Sliding Puzzle',
    slidingDesc: 'Slide numbers to solve',
    start: 'START',
    continue: 'CONTINUE',
    new: 'NEW!',
    level: 'LV',

    // Achievements
    yourAchievements: '🏆 YOUR ACHIEVEMENTS',
    starterTitle: '🎁 Starter',
    starterDesc: 'Opened first card pack!',
    artistTitle: '🎨 Artist',
    artistDesc: 'Generate 5 images',
    geniusTitle: '🧠 Genius',
    geniusDesc: 'Complete 10 games',
    unlocked: '✅ UNLOCKED',
    locked: '⏳ LOCKED',
    progress: 'Progress',

    // Language Toggle
    language: 'Language',
    indonesian: 'Indonesia',
    english: 'English'
  }
}

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('id') // Default to Indonesian

  useEffect(() => {
    const savedLanguage = localStorage.getItem('kidzplay-language')
    if (savedLanguage && translations[savedLanguage]) {
      setLanguage(savedLanguage)
    }
  }, [])

  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage)
      localStorage.setItem('kidzplay-language', newLanguage)
    }
  }

  const t = (key, params = {}) => {
    let text = translations[language][key] || translations['id'][key] || key

    // Replace parameters in text
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param])
    })

    return text
  }

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}
