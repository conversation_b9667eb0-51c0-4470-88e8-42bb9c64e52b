import { createContext, useContext, useState, useEffect } from 'react'

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

const translations = {
  id: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Selamat datang kembali, {name}! 🌟',
    searchGames: 'Cari permainan...',
    
    // Picture Collection
    myPictureCollection: '📸 KOLEKSI GAMBARKU ({count})',
    generateNewImage: 'BUAT GAMBAR BARU',
    seeAll: 'Lihat semua →',
    
    // Games Section
    startPlaying: '🎮 MULAI BERMAIN',
    memoryCardGame: 'Permainan Kartu Memori',
    memoryCardDesc: 'Cocokkan pasangan kartu',
    puzzleGame: 'Permainan Puzzle',
    puzzleDesc: 'Seret & lepas potongan puzzle',
    slidingPuzzle: 'Puzzle Geser',
    slidingDesc: '<PERSON>eser angka untuk menyelesaikan',
    start: 'MULAI',
    continue: 'LANJUTKAN',
    new: 'BARU!',
    level: 'LV',
    
    // Achievements
    yourAchievements: '🏆 PENCAPAIANMU',
    starterTitle: '🎁 Pemula',
    starterDesc: 'Membuka paket kartu pertama!',
    artistTitle: '🎨 Seniman',
    artistDesc: 'Buat 5 gambar',
    geniusTitle: '🧠 Jenius',
    geniusDesc: 'Selesaikan 10 permainan',
    unlocked: '✅ TERBUKA',
    locked: '⏳ TERKUNCI',
    progress: 'Progres',
    
    // Language Toggle
    language: 'Bahasa',
    indonesian: 'Indonesia',
    english: 'English'
  },
  en: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Welcome back, {name}! 🌟',
    searchGames: 'Search games...',
    
    // Picture Collection
    myPictureCollection: '📸 MY PICTURE COLLECTION ({count})',
    generateNewImage: 'GENERATE NEW IMAGE',
    seeAll: 'See all →',
    
    // Games Section
    startPlaying: '🎮 START PLAYING',
    memoryCardGame: 'Memory Card Game',
    memoryCardDesc: 'Match pairs of cards',
    puzzleGame: 'Puzzle Game',
    puzzleDesc: 'Drag & drop puzzle pieces',
    slidingPuzzle: 'Sliding Puzzle',
    slidingDesc: 'Slide numbers to solve',
    start: 'START',
    continue: 'CONTINUE',
    new: 'NEW!',
    level: 'LV',
    
    // Achievements
    yourAchievements: '🏆 YOUR ACHIEVEMENTS',
    starterTitle: '🎁 Starter',
    starterDesc: 'Opened first card pack!',
    artistTitle: '🎨 Artist',
    artistDesc: 'Generate 5 images',
    geniusTitle: '🧠 Genius',
    geniusDesc: 'Complete 10 games',
    unlocked: '✅ UNLOCKED',
    locked: '⏳ LOCKED',
    progress: 'Progress',
    
    // Language Toggle
    language: 'Language',
    indonesian: 'Indonesia',
    english: 'English'
  }
}

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('id') // Default to Indonesian

  useEffect(() => {
    const savedLanguage = localStorage.getItem('kidzplay-language')
    if (savedLanguage && translations[savedLanguage]) {
      setLanguage(savedLanguage)
    }
  }, [])

  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage)
      localStorage.setItem('kidzplay-language', newLanguage)
    }
  }

  const t = (key, params = {}) => {
    let text = translations[language][key] || translations['id'][key] || key
    
    // Replace parameters in text
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param])
    })
    
    return text
  }

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}
